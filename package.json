{"name": "imagen-frontend", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "nuxt typecheck", "translate-locales": "./node_modules/.bin/translate-locales", "i18n:check": "node scripts/check-missing-translations.js", "i18n:fix": "node scripts/fix-missing-translations.js", "i18n:add-missing": "node scripts/add-missing-keys.js", "i18n:validate": "node scripts/validate-i18n.js"}, "dependencies": {"@iconify-json/lucide": "^1.2.40", "@iconify-json/simple-icons": "^1.2.33", "@nuxt/content": "^3.5.1", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "^3.1.1", "@nuxtjs/supabase": "1.5.1", "@pinia/nuxt": "^0.11.0", "@supabase/supabase-js": "^2.49.8", "@vueuse/nuxt": "^13.1.0", "animate.css": "^4.1.1", "animejs": "^4.0.2", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "motion-v": "^1.0.1", "nuxt": "^3.17.2", "nuxt-og-image": "^5.1.3", "nuxt-vue3-google-signin": "^0.0.11", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "simplex-noise": "^4.0.3", "three": "^0.176.0", "translate-locales": "^1.1.2", "vue-audio-visual": "^3.0.11", "vue-number-animation": "2.0.2", "vue3-google-signin": "^2.1.1", "wavesurfer.js": "^7.9.5"}, "devDependencies": {"@nuxt/eslint": "^1.3.0", "@nuxtjs/i18n": "^9.5.4", "@types/crypto-js": "^4.2.2", "eslint": "^9.26.0", "sass-embedded": "^1.88.0", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"onlyBuiltDependencies": ["sharp"], "ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "vue-demi"]}, "packageManager": "pnpm@10.10.0"}